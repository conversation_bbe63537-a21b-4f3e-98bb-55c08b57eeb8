'use strict';

const audioUtils = require('../services/roleplay/roleplaysessions/audioUtils');
const {AUDIO_PROCESSING_DEFAULTS} = require('../constants/constant');

module.exports = {
  name: 'roleplaySTTMixin',
  methods: {
    async _handleStudentAudioChunk(state, audioChunk, format, socket) {
      state.chunkQueue.push({audioChunk, format});
      if (state.isHandlingChunk) return;
      state.isHandlingChunk = true;
      try {
        while (state.chunkQueue.length > 0) {
          const {audioChunk: currentAudioChunk, format: currentFormat} = state.chunkQueue.shift();
          if (currentFormat) {
            state.clientAudioFormat = {
              sampleRate: currentFormat.sampleRate || state.clientAudioFormat.sampleRate,
              channels: currentFormat.channels || state.clientAudioFormat.channels,
              bitDepth: currentFormat.bitsPerSample || state.clientAudioFormat.bitDepth,
            };
          }
          state.allAudioChunksForSession.push(currentAudioChunk);
          await this.processVoiceActivityAndStreamSTT(state, currentAudioChunk, socket);
        }
      } catch (err) {
        this.logger.error(`_handleStudentAudioChunk error for session ${state.sessionId}:`, err);
        if (socket) socket.emit('server:error', {message: 'Lỗi xử lý audio chunk phía server.'});
      } finally {
        state.isHandlingChunk = false;
      }
    },

    async processVoiceActivityAndStreamSTT(state, audioChunk, socket) {
      try {
        state.audioBuffer = Buffer.concat([state.audioBuffer, audioChunk]);
        const {sampleRate, channels, bitDepth} = state.clientAudioFormat;
        const windowSamples = state.sherpaVad.config?.sileroVad?.windowSize || audioUtils.VAD_DEFAULTS.windowSize;
        const frameBytes = windowSamples * (bitDepth / 8) * channels;
        while (state.audioBuffer.length >= frameBytes) {
          const frame = state.audioBuffer.slice(0, frameBytes);
          state.audioBuffer = state.audioBuffer.slice(frameBytes);
          const floatFrame = audioUtils.pcm16ToFloat32(frame);
          state.sherpaVad.acceptWaveform(floatFrame);
          if(state.sherpaVad.isDetected()){
            state.lastVoiceActivity = Date.now();
          }
          while (!state.sherpaVad.isEmpty()) {
            const seg = state.sherpaVad.front();
            state.sherpaVad.pop();
            const buf = audioUtils.float32ToPcm16(seg.samples);
            if (buf.length > 0) {
              if (!state.isStudentSpeaking) {
                // Bắt đầu turn
                state.isStudentSpeaking = true;
                state.currentStudentTranscript = '';
                if (state.isAiResponding) {
                  state.isAiInterruptedByStudent = true;
                  if (socket) socket.emit('server:ai_interrupted', {sessionId: state.sessionId});
                }
                const {streamId} = await this.broker.call('roleplay.speechprocessing.initializeSpeechStream', {
                  language: 'vi-VN',
                  sessionId: state.sessionId,
                });
                state.sttStreamId = streamId;
                state.startTurnTime = new Date();
              }
              state.currentStudentAudioChunks.push(buf);
              if (state.sttStreamId) {
                // tiếp tục turn
                await this.broker.call('roleplay.speechprocessing.pushAudioToStream', {
                  streamId: state.sttStreamId,
                  audioChunk: buf,
                });
              }
            }
          }
        }
        // Kiểm tra thời gian silence để quyết định kết thúc turn
        const silenceTime = Date.now() - state.lastVoiceActivity;
        const maxTurnTime = Date.now() - (state.startTurnTime?.getTime() || Date.now());

        if (state.isStudentSpeaking) {
          // Kiểm tra nếu đã vượt quá thời gian tối đa cho một turn
          if (maxTurnTime > AUDIO_PROCESSING_DEFAULTS.maxTurnDuration) {
            this.logger.warn(`Turn exceeded maximum duration for session ${state.sessionId}, forcing end`);
            state.isStudentSpeaking = false;
            state.isAiInterruptedByStudent = false;
            if (state.sttStreamId) {
              await this.broker.call('roleplay.speechprocessing.closeSpeechStream', {streamId: state.sttStreamId});
              state.sttStreamId = null;
            }
          }
          // Kiểm tra silence threshold với cơ chế double-check
          else if (silenceTime > AUDIO_PROCESSING_DEFAULTS.silenceThreshold) {
            console.log("SILENCE TIME", silenceTime, AUDIO_PROCESSING_DEFAULTS.silenceThreshold);

            // Đánh dấu bắt đầu quá trình xác nhận kết thúc turn
            if (!state.turnEndingConfirmation) {
              state.turnEndingConfirmation = {
                startTime: Date.now(),
                confirmed: false
              };
              this.logger.info(`Starting turn end confirmation for session ${state.sessionId}`);
              return; // Chờ thêm để xác nhận
            }

            // Nếu đã trong quá trình xác nhận và vẫn im lặng, kết thúc turn
            const confirmationTime = Date.now() - state.turnEndingConfirmation.startTime;
            if (confirmationTime > 1000) { // Chờ thêm 1 giây để xác nhận
              state.isStudentSpeaking = false;
              state.isAiInterruptedByStudent = false;
              state.turnEndingConfirmation = null;

              if (state.sttStreamId) {
                await this.broker.call('roleplay.speechprocessing.closeSpeechStream', {streamId: state.sttStreamId});
                state.sttStreamId = null;
              }
            } else {
              return; // Vẫn đang trong quá trình xác nhận
            }
          }
          // Reset confirmation nếu có voice activity trở lại
          else if (state.turnEndingConfirmation && silenceTime < AUDIO_PROCESSING_DEFAULTS.confirmationSilenceThreshold) {
            state.turnEndingConfirmation = null;
            this.logger.info(`Turn end confirmation reset due to voice activity for session ${state.sessionId}`);
            return;
          }
          // Nếu chưa đủ silence time, tiếp tục
          else {
            return;
          }
        } else {
          return; // Không phải đang trong turn của student
        }

        // Chỉ xử lý turn khi thực sự kết thúc (không còn isStudentSpeaking)
        if (!state.isStudentSpeaking && state.currentStudentTranscript && state.currentStudentTranscript.trim()) {
          state.audioBuffer = Buffer.alloc(0);
          // Xử lý turn vừa xong
          let turnAudioId = null;
          if (state.currentStudentAudioChunks && state.currentStudentAudioChunks.length > 0) {
            try {
              const audioInfo = await this.saveStudentTurnAudio(state, [...state.currentStudentAudioChunks]);
              if (audioInfo && audioInfo.fileId) {
                turnAudioId = audioInfo.fileId;
              }
            } catch (saveError) {
              this.logger.error(`Lỗi khi lưu audio lượt nói của học sinh cho session ${state.sessionId}:`, saveError);
            }
          }
          // Luôn reset currentStudentAudioChunks sau khi đã xử lý (lưu hoặc không)
          state.currentStudentAudioChunks = [];

          const durationInSeconds = (Date.now() - state.startTurnTime) / 1000.0;
          const wordCount = state.currentStudentTranscript.trim().split(/\s+/).filter(Boolean).length;
          const speakSpeed = durationInSeconds > 0 ? wordCount / durationInSeconds : 0;

          state.conversationHistory.push({
            role: 'user',
            content: state.currentStudentTranscript.trim(),
            turnAudioId: turnAudioId, // Thêm ID audio của lượt nói
            duration: durationInSeconds,
            speakSpeed: speakSpeed,
            // timestamp: new Date(state.startTime + offset) // Cân nhắc việc sử dụng offset nếu cần chính xác
          });

          console.log('state.currentStudentTranscript.trim()', state.currentStudentTranscript.trim());

          if (state.socket) {
            state.socket.emit('server:student_text_response', {
              sessionId: state.sessionId,
              text: state.currentStudentTranscript.trim(),
              role: 'user',
              isFinal: true,
              turnAudioId: turnAudioId, // Có thể gửi kèm về client nếu cần
            });
          }

          // Reset transcript sau khi đã xử lý
          state.currentStudentTranscript = '';

          // Trigger AI response với delay nhỏ để đảm bảo transcript đã được xử lý hoàn chỉnh
          setTimeout(async () => {
            if (!state.isStudentSpeaking && !state.isAiResponding) {
              await this.processLLMResponseStreamAll(state, state.socket);
            }
          }, 200);
        }
      } catch (err) {
        this.logger.error(`processVoiceActivityAndStreamSTT error for session ${state.sessionId}:`, err);
        if (state.sttStreamId)
          await this.broker.call('roleplay.speechprocessing.closeSpeechStream', {streamId: state.sttStreamId});
        state.isStudentSpeaking = false;
        state.audioBuffer = Buffer.alloc(0);
        if (socket) socket.emit('server:error', {message: 'Lỗi xử lý giọng nói của bạn.'});
      }
    },
  },
};
