'use strict';

/**
 * Mixin chứa logic LLM và TTS streaming cho phiên roleplay.
 */
module.exports = {
  name: 'roleplayLLMMixin',
  methods: {
    // Tạm dừng bằng Promise
    async timeout(delay) {
      return new Promise(res => setTimeout(res, delay));
    },

    // Tính delay giữa các chunk audio dựa trên kích thước chunk và format
    calculateAudioChunkDelay(chunkSize, audioFormat) {
      const { sampleRate = 16000, channels = 1, bitDepth = 16 } = audioFormat;
      const bytesPerSample = bitDepth / 8;
      const samplesInChunk = chunkSize / (channels * bytesPerSample);
      const durationMs = (samplesInChunk / sampleRate) * 1000;
      // 85% thời gian thực tế, tối thiểu 50ms
      return Math.max(durationMs * 0.85, 50);
    },

    // Stream audio buffer về client với điều khiển tốc độ, hỗ trợ ngắt lời
    async streamAudioWithRateControl(state, audioBuffer, socket, turnId, wasInterruptedRef) {
      const chunkSize = 4096;
      const audioFormat = { sampleRate: 16000, channels: 1, bitDepth: 16 };
      let offset = 0;
      let chunkIndex = 0;
      this.logger.info(`[${turnId}] Bắt đầu stream audio, tổng bytes: ${audioBuffer.length}`);
      while (offset < audioBuffer.length) {
        if (state.isAiInterruptedByStudent) {
          if (wasInterruptedRef && typeof wasInterruptedRef === 'object') wasInterruptedRef.value = true;
          this.logger.info(`[${turnId}] AI bị ngắt lời tại chunk ${chunkIndex}`);
          socket.emit('server:ai_speech_interrupted', {
            sessionId: state.sessionId,
            turnId,
            bytesSent: offset,
            totalBytes: audioBuffer.length,
          });
          break;
        }
        const end = Math.min(offset + chunkSize, audioBuffer.length);
        const audioChunkData = audioBuffer.slice(offset, end);
        const isLast = end >= audioBuffer.length;
        socket.emit('server:ai_speech_chunk', {
          sessionId: state.sessionId,
          audioChunk: audioChunkData,
          chunkIndex,
          offset,
          totalSize: audioBuffer.length,
          isLast,
          turnId,
        });
        offset = end;
        chunkIndex++;
        if (!isLast) {
          const delay = this.calculateAudioChunkDelay(chunkSize, audioFormat);
          await this.timeout(delay);

          // Kiểm tra cả isAiInterruptedByStudent và isStudentSpeaking
          if (state.isAiInterruptedByStudent || state.isStudentSpeaking) {
            if (wasInterruptedRef && typeof wasInterruptedRef === 'object') wasInterruptedRef.value = true;
            socket.emit('server:ai_speech_interrupted', {
              sessionId: state.sessionId,
              turnId,
              bytesSent: offset,
              totalBytes: audioBuffer.length,
              reason: state.isStudentSpeaking ? 'student_speaking' : 'interrupted'
            });
            this.logger.info(`[${state.sessionId}] AI speech interrupted: ${state.isStudentSpeaking ? 'student speaking' : 'interrupted'}`);
            break;
          }
        }
      }
      if (!state.isAiInterruptedByStudent) {
        this.logger.info(`[${turnId}] Hoàn thành stream audio: ${offset} bytes, ${chunkIndex} chunks`);
      }
    },

    // Xử lý TTS cho một câu đơn
    async processSingleSentenceToSpeech(state, sentence, socket) {
      const ttsId = `ttsSentence_${state.sessionId}_${Date.now()}`;
      console.time(ttsId);
      try {
        if (!sentence || !sentence.trim()) return null;
        const audioBuff = await this.broker.call('roleplay.speechprocessing.textToSpeech', {
          provider: 'microsoft',
          text: sentence,
          paramInstructions: state.persona?.voiceStyle,
          voice: state.persona?.voice,
        });
        if (audioBuff && audioBuff.length > 0) {
          const ref = { value: false };
          await this.streamAudioWithRateControl(state, audioBuff, socket, ttsId, ref);
          return audioBuff;
        }
        return null;
      } catch (err) {
        this.logger.error(`Lỗi TTS cho câu '${sentence}' trong session ${state.sessionId}:`, err);
        socket.emit('server:error', { sessionId: state.sessionId, message: `Lỗi TTS: ${sentence}` });
        return null;
      } finally {
        console.timeEnd(ttsId);
      }
    },

    // Xử lý gọi LLM (stream + TTS toàn bộ văn bản)
    async processLLMResponseStreamAll(state, socket) {
      const llmId = `llmStreamAll_${state.sessionId}_${Date.now()}`;
      console.time(llmId);
      let fullText = '';
      let wasInterrupted = false;
      state.currentAiTurnAudioChunks = [];

      try {
        // Kiểm tra kỹ lưỡng trước khi bắt đầu AI response
        if (state.isStudentSpeaking) {
          this.logger.info(`[${state.sessionId}] Skipping AI response - student is still speaking`);
          return;
        }

        if (state.isAiResponding) {
          this.logger.info(`[${state.sessionId}] Skipping AI response - AI is already responding`);
          return;
        }

        if (state.isAiInterruptedByStudent) {
          this.logger.info(`[${state.sessionId}] Skipping AI response - AI was interrupted by student`);
          return;
        }

        // Double check với delay nhỏ để đảm bảo không có voice activity mới
        await new Promise(resolve => setTimeout(resolve, 300));

        if (state.isStudentSpeaking) {
          this.logger.info(`[${state.sessionId}] Skipping AI response after delay check - student started speaking`);
          return;
        }

        state.isAiResponding = true;
        const messages = state.conversationHistory;
        const stream = await this.broker.call('roleplay.openai.chatCompletionStream', {
          messages,
          model: 'gpt-4.1',
          temperature: 1,
          max_tokens: 500,
        });
        socket.emit('server:ai_processing_started', { sessionId: state.sessionId });
        for await (const chunk of stream) {
          // Kiểm tra interruption hoặc student bắt đầu nói
          if (state.isAiInterruptedByStudent || state.isStudentSpeaking) {
            wasInterrupted = true;
            this.logger.info(`[${state.sessionId}] AI LLM stream interrupted`);
            break;
          }

          const delta = chunk.choices[0]?.delta?.content || '';
          if (delta) {
            fullText += delta;
            socket.emit('server:ai_text_chunk_response', {
              sessionId: state.sessionId,
              textChunk: delta,
              role: 'assistant',
              turnId: llmId,
            });
          }
        }
        // Kiểm tra lại trước khi bắt đầu TTS
        if (state.isAiInterruptedByStudent || state.isStudentSpeaking) {
          this.logger.info(`[${state.sessionId}] Skipping TTS - interrupted or student speaking`);
          return;
        }

        if (fullText.trim()) {
          socket.emit('server:ai_tts_started', { sessionId: state.sessionId });

          // Kiểm tra lần cuối trước khi gọi TTS
          if (state.isAiInterruptedByStudent || state.isStudentSpeaking) {
            this.logger.info(`[${state.sessionId}] Skipping TTS call - interrupted during preparation`);
            socket.emit('server:ai_tts_completed', { sessionId: state.sessionId });
            return;
          }

          const audioBuff = await this.broker.call('roleplay.speechprocessing.textToSpeech', {
            provider: 'microsoft',
            text: fullText.trim(),
            stream: true,
            paramInstructions: state.persona?.voiceStyle,
            voice: state.persona?.voice,
          });
          // Kiểm tra lần cuối trước khi stream audio
          if (state.isAiInterruptedByStudent || state.isStudentSpeaking) {
            this.logger.info(`[${state.sessionId}] Skipping audio streaming - interrupted after TTS`);
            socket.emit('server:ai_tts_completed', { sessionId: state.sessionId });
            return;
          }

          if (audioBuff && audioBuff.length > 0) {
            state.currentAiTurnAudioChunks.push(audioBuff);
            socket.emit('server:ai_text_response', {
              sessionId: state.sessionId,
              text: fullText.trim(),
              role: 'assistant',
              isFinal: true,
              turnId: llmId,
            });
            const ref = { value: false };
            await this.streamAudioWithRateControl(state, audioBuff, socket, llmId, ref);
            wasInterrupted = ref.value;
          }
        }
        let turnAudioId = null;
        let turnDuration = 0;
        if (!wasInterrupted && state.currentAiTurnAudioChunks.length > 0) {
          const info = await this.saveAiTurnAudio(state, [...state.currentAiTurnAudioChunks]);
          if (info && info.fileId) { turnAudioId = info.fileId; turnDuration = info.duration || 0; }
        }
        if (!wasInterrupted) {
          state.conversationHistory.push({
            role: 'assistant',
            content: fullText.trim(),
            turnAudioId,
            duration: turnDuration,
            speakSpeed: turnDuration > 0 ? fullText.trim().split(/\s+/).length / turnDuration : 0,
          });
          socket.emit('server:ai_response_completed', {
            sessionId: state.sessionId,
            fullText: fullText.trim(),
            turnAudioId,
            duration: turnDuration,
          });
        }
      } catch (err) {
        this.logger.error(`Lỗi LLM/TTS trong session ${state.sessionId}:`, err);
        socket.emit('server:error', { sessionId: state.sessionId, message: 'Lỗi xử lý phản hồi AI.' });
      } finally {
        state.isAiResponding = false;
        state.currentAiTurnAudioChunks = [];
        socket.emit('server:ai_tts_completed', { sessionId: state.sessionId });
        console.timeEnd(llmId);
      }
    },
  },
};
