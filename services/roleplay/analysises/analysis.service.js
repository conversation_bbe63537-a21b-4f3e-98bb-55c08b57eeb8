'use strict';

const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const FileMixin = require('../../../mixins/file.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./analysis.model');
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const {path} = require('@ffmpeg-installer/ffmpeg');

// <PERSON><PERSON><PERSON> thành phần prompt phân tích được tách thành const
const ANALYSIS_SYSTEM_INSTRUCTION = `Bạn là một chuyên gia phân tích role play, có kinh nghiệm phân tích trao đổi giữa các học viên và AI persona trong các kịch bản đào tạo kỹ năng.
Nhiệm vụ của bạn là đánh giá chất lượng cuộc trò chuyện và cung cấp phản hồi chi tiết để giúp học viên cải thiện kỹ năng.`;

const KNOWLEDGE_ANALYSIS_INSTRUCTION = `
# PHÂN TÍCH KIẾN THỨC

## Đánh giá chung về Quy trình thành thạo (Proficiency Process) cho toàn bộ Khóa học:
Cung cấp một đánh giá tổng quan về mức độ học viên nắm vững và thực hiện đúng quy trình chung của toàn bộ khóa học. Nhận xét này nên tập trung vào khả năng liên kết các nhiệm vụ và hiểu bức tranh tổng thể.

## Đánh giá chi tiết cho từng Nhiệm Vụ (Task) trong Khóa học:
Với MỖI nhiệm vụ (task) đã được cung cấp trong thông tin bối cảnh của khóa học, hãy đánh giá riêng dựa trên các tiêu chí sau và đưa ra điểm số từ 0-100 CHO TỪNG TASK:
1.  **Độ chính xác của thông tin và kiến thức chuyên môn được sử dụng LIÊN QUAN ĐẾN TASK ĐÓ. Chỉ các thông tin sai của thông tin so với tài liệu khóa học.**
2.  **Khả năng áp dụng đúng quy trình và nguyên tắc LIÊN QUAN ĐẾN TASK ĐÓ.**
3.  **Xác định các "yếu tố then chốt" (Make or break) TRONG PHẦN HỘI THOẠI LIÊN QUAN ĐẾN TASK ĐÓ - những điểm quyết định thành công hay thất bại của task. Chỉ ra thông tin liên quan trong tài liệu**
4.  **Đưa ra các gợi ý (suggestions) cụ thể để cải thiện kỹ năng cho TASK ĐÓ. Nếu thông tin có trong tài liệu, hãy trích dẫn tài liệu**

LƯU Ý QUAN TRỌNG: Phải có đủ các mục (score, details, makeOrBreak, suggestions) cho TỪNG TASK được liệt kê trong bối cảnh. Nếu một task không có phần hội thoại nào liên quan rõ ràng, hãy ghi nhận điều đó trong phần details của task đó và cho điểm phù hợp (ví dụ: 0 hoặc N/A nếu không thể đánh giá).
`;

const STYLE_ANALYSIS_INSTRUCTION = `
# PHÂN TÍCH PHONG CÁCH (50% tổng điểm)
Đánh giá phong cách giao tiếp của học viên dựa trên các tiêu chí sau:
1. Rõ ràng (Clarity): Đánh giá mức thấp, trung bình hoặc cao
2. Tốc độ (Pace): Đánh giá dựa trên số từ/phút, mức 140-180 từ/phút là lý tưởng
3. Từ đệm (Filler words): Đánh giá việc sử dụng các từ như "ừm", "à", v.v.
4. Độ dài câu (Sentence length): Đánh giá độ phù hợp, 1-30 từ là tốt nhất
5. Năng lượng (Energy): Đánh giá sự sinh động trong giọng nói và cách diễn đạt

Hãy đưa ra điểm số cho phần này từ 0-100 dựa trên các tiêu chí trên.
`;

const TRAINER_FEEDBACK_INSTRUCTION = `
# PHẢN HỒI HLV (AI TRAINER FEEDBACK)
Đưa ra nhận xét tổng quát về hiệu suất của học viên và các gợi ý cụ thể để cải thiện. Nội dung phản hồi cần:
1. Tóm tắt những điểm mạnh chính
2. Chỉ ra những cơ hội cải thiện quan trọng nhất
3. Đưa ra 2-3 ví dụ cụ thể về cách học viên có thể cải thiện
4. Nhấn mạnh các chiến lược cụ thể mà học viên có thể áp dụng trong tương lai
5. Chỉ ra các lỗi sai của học viên mắc phải và cách họ có thể khắc phục nhất là các lỗi sai so với tài liệu kèm theo
`;

const RESPONSE_FORMAT = `
# FORMAT PHẢN HỒI
Hãy trả lời dưới dạng JSON với cấu trúc sau:
{
  "summary": "Tóm tắt ngắn gọn về hiệu suất tổng thể",
  "topInsights": ["Insight 1", "Insight 2", "Insight 3"],
  "simulationScore": XX, // Tổng điểm (average của styleScore và trung bình các task knowledge scores)
  "knowledgeAnalysis": {
    "proficiencyProcess": "Đánh giá tổng quan về quy trình thành thạo của toàn bộ khóa học",
    "taskAnalyses": [
      {
        "taskId": "ID của task 1 (sẽ được cung cấp trong prompt hoặc bạn cần suy ra từ tên)",
        "taskName": "Tên của Task 1",
        "score": XX, // Điểm phần kiến thức cho Task 1 (0-100)
        "details": "Đánh giá chi tiết về kiến thức cho Task 1 , Nêu đầy đủ lỗi sai của học viên task 1",
        "makeOrBreak": ["Yếu tố then chốt 1 cho Task 1", "Yếu tố then chốt 2 cho Task 1"],
        "suggestions": ["Gợi ý 1 cho Task 1", "Gợi ý 2 cho Task 1"]
      },
      {
        "taskId": "ID của task 2",
        "taskName": "Tên của Task 2",
        "score": YY,
        "details": "Đánh giá chi tiết về kiến thức cho Task 2, , Nêu đầy đủ lỗi sai của học viên task 2",
        "makeOrBreak": ["Yếu tố then chốt 1 cho Task 2"],
        "suggestions": ["Gợi ý 1 cho Task 2"]
      }
      // ... thêm các task analyses khác nếu có
    ]
  },
  "styleAnalysis": {
    "score": XX, // Điểm phần phong cách (0-100)
    "clarity": "low/medium/high",
    "pace": {
      "wordsPerMinute": XXX,
      "evaluation": "too slow/good/too fast"
    },
    "fillerWords": {
      "count": XX,
      "evaluation": "Đánh giá về từ đệm"
    },
    "sentenceLength": {
      "average": XX,
      "evaluation": "Đánh giá về độ dài câu"
    },
    "energy": "low/medium/high"
  },
  "trainerFeedback": {
    "generalComments": "Nhận xét tổng quát",
    "improvementSuggestions": ["Gợi ý cải thiện 1", "Gợi ý cải thiện 2", "Gợi ý cải thiện 3"]
  }
}
`;

module.exports = {
  name: 'roleplay.analysises',
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin],

  settings: {
    entityValidator: {},
    populates: {
      sessionId: 'roleplaysessions.get',
      createdBy: 'users.get',
      updatedBy: 'users.get',
    },
    populateOptions: ['sessionId', 'createdBy', 'updatedBy'],
  },

  actions: {
    /**
     * Phân tích phiên roleplay
     */
    analyze: {
      rest: 'POST /analyze',
      params: {
        sessionId: 'string',
        options: {
          type: 'object',
          optional: true,
          properties: {
            includeEmotionAnalysis: {type: 'boolean', optional: true, default: false},
            includeSpeechAnalysis: {type: 'boolean', optional: true, default: false},
            includeVideoAnalysis: {type: 'boolean', optional: true, default: false},
            includeSoftSkillsAnalysis: {type: 'boolean', optional: true, default: false},
            includeManagerFeedback: {type: 'boolean', optional: true, default: false},
          },
        },
      },
      async handler(ctx) {
        const {sessionId, options = {}, forceUpdate = false} = ctx.params;
        const creatorUserId = ctx.meta.user?._id;

        return this._performAndSaveAnalysis(sessionId, options, creatorUserId, forceUpdate);
      },
    },

    /**
     * Lấy kết quả phân tích cho phiên cụ thể
     */
    getAnalysisForSession: {
      rest: 'GET',
      auth: 'required',
      params: {
        sessionId: 'string',
      },
      async handler(ctx) {
        const {sessionId} = ctx.params;

        const analysis = await this.adapter.findOne({sessionId, isDeleted: false});
        if (!analysis) {
          return null;
        }

        return this.transformDocuments(ctx, {}, analysis);
      },
    },
  },

  events: {
    'roleplay.session.completed_for_analysis': {
      group: 'local', // Hoặc group phù hợp với kiến trúc của bạn
      async handler(payload) {
        const {sessionId, sessionData} = payload;
        this.logger.info(`Nhận sự kiện 'roleplay.session.completed_for_analysis' cho sessionId: ${sessionId}`);
        try {
          // options có thể được lấy từ sessionData hoặc cấu hình mặc định
          const options = {
            includeEmotionAnalysis: false, // Ví dụ: lấy từ cấu hình hoặc logic nghiệp vụ
            includeSpeechAnalysis: false,
            // ... các options khác
          };
          const creatorUserId = sessionData?.createdBy || sessionData?.studentId; // Ưu tiên createdBy nếu có

          if (!creatorUserId) {
            this.logger.warn(
              `Không tìm thấy creatorUserId (từ createdBy hoặc studentId) cho session ${sessionId}. Phân tích có thể không được tạo.`,
            );
            return;
          }

          await this._performAndSaveAnalysis(sessionId, options, creatorUserId);
        } catch (error) {
          this.logger.error(
            `Lỗi khi xử lý sự kiện 'roleplay.session.completed_for_analysis' cho session ${sessionId}:`,
            error,
          );
          // Cân nhắc phát một sự kiện lỗi nếu cần thiết
        }
      },
    },
  },

  methods: {
    /**
     * Thực hiện và lưu trữ phân tích
     */
    async _performAndSaveAnalysis(sessionId, options = {}, creatorUserId, forceUpdate = false) {
      const {
        includeEmotionAnalysis = true,
        includeSpeechAnalysis = true,
        includeVideoAnalysis = false,
        includeSoftSkillsAnalysis = false,
        includeManagerFeedback = false,
      } = options;

      try {
        // Kiểm tra xem phiên đã tồn tại chưa
        const existingAnalysis = await this.adapter.findOne({sessionId, isDeleted: false});
        if (existingAnalysis && !forceUpdate) {
          this.logger.info(`Phân tích cho session ${sessionId} đã tồn tại. ID: ${existingAnalysis._id}`);
          // Không phát sự kiện nếu đã có phân tích, hoặc có thể phát sự kiện cập nhật nếu muốn
          return existingAnalysis; // Trả về phân tích đã có
        }

        // Lấy thông tin phiên, populate các trường cần thiết
        const session = await this.broker.call('roleplaysessions.get', {
          id: sessionId,
          populate: ['courseId', 'personaId', 'studentId', 'recordingId', 'taskIds'], // taskIds giờ ở trong session, không còn trong course
        });

        if (!session) {
          throw new MoleculerClientError(`Không tìm thấy phiên roleplay ${sessionId} để phân tích`, 404);
        }

        // Lấy nội dung cuộc trò chuyện
        const conversationHistory = session.transcripts;

        // if (!conversationHistory || conversationHistory.length === 0) {
        //   throw new MoleculerClientError(`Phiên ${sessionId} không có nội dung hội thoại để phân tích`, 400);
        // }

        // Lấy thông tin AI Persona và Khóa học (bao gồm các tasks)
        const persona = session.personaId; // Sử dụng personaId đã được populate
        // const task = session.taskId; // Loại bỏ task đơn lẻ
        const course = session.courseId; // courseId từ session giờ đây nên là object Course đầy đủ với tasks đã populate

        if (!session.taskIds || session.taskIds.length === 0) {
          this.logger.warn(
            `Session ${sessionId} không có thông tin taskIds (danh sách tasks) hoặc rỗng. Phân tích có thể không chính xác.`,
          );
          // Có thể throw lỗi hoặc xử lý mềm tùy theo yêu cầu
        }

        // Thực hiện phân tích cơ bản
        const analysisResult = await this.performAnalysis(
          conversationHistory,
          persona,
          // task, // Loại bỏ task
          course, // Truyền course object (đã bao gồm tasks)
          session,
        );

        // Thực hiện phân tích giọng nói nếu cần
        let speechAnalysis = null;
        // Sửa: sử dụng session.recordingId thay vì session.studentAudioId
        if (includeSpeechAnalysis && session.recordingId) {
          speechAnalysis = await this.analyzeSpeech(session);
        }

        // Thực hiện phân tích cảm xúc nếu cần
        let emotionAnalysis = null;
        if (includeEmotionAnalysis) {
          emotionAnalysis = await this.analyzeEmotions(conversationHistory);
        }

        // Thực hiện phân tích video nếu cần
        let videoAnalysis = null;
        if (includeVideoAnalysis) {
          try {
            videoAnalysis = await this.broker.call('roleplay.videoanalysis.analyze', {sessionId});
          } catch (videoError) {
            this.logger.warn(`Không thể phân tích video cho session ${sessionId}:`, videoError);
          }
        }
        // Thực hiện phân tích kỹ năng mềm nếu cần
        let softSkillsAnalysis = null;
        if (includeSoftSkillsAnalysis) {
          try {
            softSkillsAnalysis = await this.broker.call('roleplay.softskillsanalysis.analyze', {sessionId});
          } catch (softError) {
            this.logger.warn(`Không thể phân tích kỹ năng mềm cho session ${sessionId}:`, softError);
          }
        }
        // Lấy feedback từ quản lý nếu cần
        let managerFeedbackResult = null;
        if (includeManagerFeedback) {
          try {
            managerFeedbackResult = await this.broker.call('roleplay.managerfeedback.getForSession', {sessionId});
          } catch (mgrError) {
            this.logger.warn(`Không thể lấy feedback quản lý cho session ${sessionId}:`, mgrError);
          }
        }

        // Kết hợp tất cả các kết quả phân tích
        const finalResult = this.combineAnalysisResults(analysisResult, speechAnalysis, emotionAnalysis);
        // Gán các kết quả phân tích mở rộng
        finalResult.videoAnalysis = videoAnalysis;
        finalResult.softSkillsAnalysis = softSkillsAnalysis;
        finalResult.managerFeedback = managerFeedbackResult;

        // Lưu kết quả phân tích vào cơ sở dữ liệu
        const analysis = await this.adapter.insert({
          sessionId,
          result: finalResult,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: creatorUserId, // Sử dụng creatorUserId được truyền vào
          isDeleted: false,
        });

        // Phát sự kiện sau khi phân tích thành công
        this.broker.emit('roleplay.analysis.completed', {
          sessionId: sessionId,
          analysisId: analysis._id.toString(),
          analysisData: analysis, // Gửi kèm dữ liệu analysis nếu cần
        });
        this.logger.info(
          `Đã phân tích và phát sự kiện 'roleplay.analysis.completed' cho sessionId: ${sessionId}, analysisId: ${analysis._id}`,
        );

        return analysis;
      } catch (error) {
        this.logger.error(`Lỗi trong _performAndSaveAnalysis cho session ${sessionId}:`, error);
        // Không ném lỗi ở đây để event handler không bị crash, nhưng ghi log lỗi
        // Hoặc có thể throw lỗi nếu muốn transaction bị rollback hoặc xử lý ở tầng cao hơn
        // throw new MoleculerClientError(`Không thể thực hiện và lưu phân tích cho session ${sessionId}`, 500, error.message);
        return null; // Trả về null nếu có lỗi để không làm crash flow của event
      }
    },

    /**
     * Thực hiện phân tích cuộc trò chuyện
     */
    async performAnalysis(conversationHistory, persona, course, session) {
      try {
        // Chuẩn bị dữ liệu cho phân tích
        const formattedConversation = this.formatConversationForAnalysis(conversationHistory);

        // Xây dựng nội dung prompt cho phân tích
        const prompt = this.buildAnalysisPrompt(formattedConversation, persona, course, session);

        // Gọi OpenAI để phân tích
        const analysisResponse = await this.broker.call('roleplay.openai.sendToOpenAI', {
          messages: [
            {role: 'system', content: prompt},
            {role: 'user', content: 'Phân tích cuộc hội thoại trên và đưa ra kết quả đánh giá chi tiết.'},
          ],
          temperature: 0.3,
          maxTokens: 6000,
        });

        // Chuyển đổi kết quả từ văn bản sang JSON
        return this.parseAnalysisResponse(analysisResponse);
      } catch (error) {
        this.logger.error('Lỗi trong quá trình phân tích:', error);
        throw error;
      }
    },

    /**
     * Phân tích giọng nói từ file âm thanh
     */
    async analyzeSpeech(session) {
      try {
        // Sửa: sử dụng session.recordingId và session.transcripts (thay vì session.transcript)
        if (!session.recordingId || !session.transcripts || session.transcripts.length === 0) {
          this.logger.warn(`Phân tích giọng nói cho session ${session._id}: Thiếu recordingId hoặc transcripts.`);
          return null; // Trả về null nếu thiếu dữ liệu cần thiết
        }

        // Lấy thông tin file âm thanh
        // Sửa: sử dụng session.recordingId
        const audioFile = await this.broker.call('files.get', {id: session.recordingId});
        if (!audioFile) {
          this.logger.warn(
            `Phân tích giọng nói cho session ${session._id}: Không tìm thấy file ghi âm với ID ${session.recordingId}.`,
          );
          return null; // Trả về null nếu không tìm thấy file
        }

        // Tính toán thời lượng âm thanh (giây)
        const audioLength = session.duration || 300; // Mặc định 5 phút nếu không có thông tin

        // Ghép các transcript của student thành một chuỗi duy nhất
        const studentTranscript = session.transcripts
          .filter(t => t.role === 'student' && t.content)
          .map(t => t.content.trim())
          .join(' \n'); // Nối các lượt nói bằng dấu cách hoặc newline

        if (!studentTranscript) {
          this.logger.warn(
            `Phân tích giọng nói cho session ${session._id}: Không có transcript của học viên để phân tích.`,
          );
          return null;
        }

        // Gọi service phân tích giọng nói
        return this.broker.call('roleplay.speechprocessing.analyzeSpeech', {
          transcript: studentTranscript, // Sử dụng transcript đã ghép của học viên
          audioLength,
        });
      } catch (error) {
        this.logger.warn(`Không thể phân tích giọng nói cho session ${session?._id}:`, error.message);
        return null; // Trả về null nếu không thể phân tích, không gây lỗi toàn bộ quá trình
      }
    },

    /**
     * Phân tích cảm xúc từ nội dung cuộc trò chuyện
     */
    async analyzeEmotions(conversationHistory) {
      try {
        // Gọi service phân tích cảm xúc
        return this.broker.call('emotionanalysis.analyze', {
          conversation: conversationHistory,
        });
      } catch (error) {
        this.logger.warn('Không thể phân tích cảm xúc:', error.message);
        return null; // Trả về null nếu không thể phân tích, không gây lỗi toàn bộ quá trình
      }
    },

    /**
     * Kết hợp các kết quả phân tích
     */
    combineAnalysisResults(basicAnalysis, speechAnalysis, emotionAnalysis) {
      // Tạo bản sao sâu của phân tích cơ bản
      const combinedResult = JSON.parse(JSON.stringify(basicAnalysis));

      // Thêm phân tích giọng nói nếu có
      if (speechAnalysis) {
        // Cập nhật styleAnalysis với dữ liệu từ phân tích giọng nói
        combinedResult.styleAnalysis.pace = {
          wordsPerMinute: speechAnalysis.wordsPerMinute,
          evaluation: this.evaluatePace(speechAnalysis.wordsPerMinute),
        };

        combinedResult.styleAnalysis.fillerWords = {
          count: speechAnalysis.fillerWords.total,
          percentage: speechAnalysis.fillerWords.percentage.toFixed(2),
          details: speechAnalysis.fillerWords.details,
          evaluation: this.evaluateFillerWords(speechAnalysis.fillerWords.percentage),
        };

        combinedResult.styleAnalysis.sentenceLength = {
          average: speechAnalysis.sentences.averageLength.toFixed(1),
          evaluation: this.evaluateSentenceLength(speechAnalysis.sentences.averageLength),
        };
      }

      // Thêm phân tích cảm xúc nếu có
      if (emotionAnalysis) {
        combinedResult.emotionAnalysis = emotionAnalysis;
      }

      // Đảm bảo các trường phân tích mở rộng luôn tồn tại
      if (typeof combinedResult.videoAnalysis === 'undefined') {
        combinedResult.videoAnalysis = null;
      }
      if (typeof combinedResult.softSkillsAnalysis === 'undefined') {
        combinedResult.softSkillsAnalysis = null;
      }
      if (typeof combinedResult.managerFeedback === 'undefined') {
        combinedResult.managerFeedback = null;
      }
      // Tái tính simulationScore từ knowledge và style nếu có
      if (
        combinedResult.knowledgeAnalysis &&
        combinedResult.knowledgeAnalysis.taskAnalyses &&
        combinedResult.knowledgeAnalysis.taskAnalyses.length > 0 &&
        combinedResult.styleAnalysis &&
        typeof combinedResult.styleAnalysis.score === 'number'
      ) {
        const validTaskScores = combinedResult.knowledgeAnalysis.taskAnalyses
          .map(ta => ta.score)
          .filter(score => typeof score === 'number' && !isNaN(score));

        if (validTaskScores.length > 0) {
          const averageTaskScore = validTaskScores.reduce((sum, score) => sum + score, 0) / validTaskScores.length;
          combinedResult.simulationScore = Math.round((averageTaskScore + combinedResult.styleAnalysis.score) / 2);
        } else {
          combinedResult.simulationScore = combinedResult.styleAnalysis.score; // Hoặc một giá trị mặc định khác nếu không có task score
        }
      } else if (combinedResult.styleAnalysis && typeof combinedResult.styleAnalysis.score === 'number') {
        // Nếu chỉ có style score
        combinedResult.simulationScore = combinedResult.styleAnalysis.score;
      } else {
        // Nếu không có score nào, đặt là 0 hoặc một giá trị mặc định
        combinedResult.simulationScore = 0;
      }

      return combinedResult;
    },

    /**
     * Định dạng lại cuộc trò chuyện để phân tích
     */
    formatConversationForAnalysis(conversationHistory) {
      return conversationHistory
        .map((message, index) => {
          const role = message.role === 'student' ? 'Học viên' : 'AI Persona';
          return `[${index + 1}] ${role}: ${message.content}`;
        })
        .join('\n\n');
    },

    /**
     * Xây dựng prompt cho phân tích
     */
    buildAnalysisPrompt(formattedConversation, persona, course, session) {
      let tasksInformationForContext = 'Các nhiệm vụ trong khóa học:\n';
      let tasksInformationForGuidance = '\n\n## Hướng dẫn chi tiết cho việc phân tích từng Nhiệm Vụ:\n';

      // Lấy taskIds từ session thay vì từ course
      if (session && session.taskIds && session.taskIds.length > 0) {
        session.taskIds.forEach((task, index) => {
          const taskIdentifier = `TASK_ID_${task._id}`;
          tasksInformationForContext += `  Nhiệm vụ ${index + 1}: ${task.name || 'Không có tên'} (ID để tham chiếu trong phản hồi: ${taskIdentifier})\n`;
          if (task.description) {
            tasksInformationForContext += `    Mô tả: ${task.description}\n`;
          }
          if (task.objective) {
            tasksInformationForContext += `    Mục tiêu: ${task.objective}\n`;
          }
          tasksInformationForContext += '  ---\n';

          // Thêm thông tin cho LLM về cách trả về cho từng task
          tasksInformationForGuidance += `Đối với nhiệm vụ có tên "${task.name || 'Không có tên'}" (ID: ${taskIdentifier}), hãy cung cấp phân tích trong mảng 'taskAnalyses' với trường 'taskId' là '${taskIdentifier}' và 'taskName' là '${task.name || 'Không có tên'}' cùng với các trường score, details, makeOrBreak, suggestions của riêng nó.\n`;
        });
      } else {
        tasksInformationForContext = 'Khóa học này không có nhiệm vụ cụ thể nào được liệt kê.\n';
        tasksInformationForGuidance = '';
      }
      console.log('#####################tasksInformationForContext', tasksInformationForContext);
      console.log('#####################tasksInformationForGuidance', tasksInformationForGuidance);

      // Lấy nội dung tham khảo từ course references nếu có
      let referencesContent = '';
      if (course && course.references && course.references.length > 0) {
        referencesContent = '\n# TÀI LIỆU THAM KHẢO KHÓA HỌC\n';
        try {
          // Lấy thông tin chi tiết của các references
          const referencesInfo = course.references.map(reference => {
            try {
              if (reference && !reference.isDeleted) {
                let refInfo = `## ${reference.name || 'Tài liệu không tên'}\n`;
                if (reference.description) {
                  refInfo += `Mô tả: ${reference.description}\n`;
                }
                if (reference.content) {
                  refInfo += `Nội dung:\n${reference.content}\n`;
                } else if (reference.url) {
                  refInfo += `URL: ${reference.url}\n`;
                }
                refInfo += '---\n';
                return refInfo;
              }
              return null;
            } catch (error) {
              this.logger.warn(`Không thể lấy thông tin reference ${reference}:`, error.message);
              return null;
            }
          });

          // Đợi tất cả các promises hoàn thành
          referencesContent += referencesInfo.filter(Boolean).join('\n');

          // Nếu không có reference nào được lấy thành công
          if (!referencesInfo.filter(Boolean).length) {
            referencesContent = '';
          }
        } catch (error) {
          this.logger.warn('Lỗi khi lấy thông tin references:', error.message);
          referencesContent = '';
        }
      }

      let contextInfo = `
        # THÔNG TIN BỐI CẢNH
        - Tên AI Persona: ${persona?.name || 'Không có thông tin'}
        - Vai trò AI Persona: ${persona?.role || 'Không có thông tin'}
        - Khóa học: ${course?.name || 'Không có thông tin'}
        ${tasksInformationForContext}
        - Thời gian phiên: ${this.formatDuration(session?.startedAt, session?.endedAt)}
        `;

      // Kết hợp tất cả các phần của prompt
      // Chèn tasksInformationForGuidance vào KNOWLEDGE_ANALYSIS_INSTRUCTION trước phần RESPONSE_FORMAT
      const finalKnowledgeAnalysisInstruction = KNOWLEDGE_ANALYSIS_INSTRUCTION.replace(
        'LƯU Ý QUAN TRỌNG:',
        `${tasksInformationForGuidance}\nLƯU Ý QUAN TRỌNG:`,
      );

      return `${ANALYSIS_SYSTEM_INSTRUCTION}\n\n${contextInfo}${referencesContent}\n\n# NỘI DUNG CUỘC TRÒ CHUYỆN\n${formattedConversation}\n\n${finalKnowledgeAnalysisInstruction}\n\n${STYLE_ANALYSIS_INSTRUCTION}\n\n${TRAINER_FEEDBACK_INSTRUCTION}\n\n${RESPONSE_FORMAT}`;
    },

    /**
     * Chuyển đổi phản hồi văn bản sang JSON
     */
    parseAnalysisResponse(analysisResponse) {
      try {
        // Tìm chuỗi JSON trong phản hồi
        const jsonStartIndex = analysisResponse.indexOf('{');
        const jsonEndIndex = analysisResponse.lastIndexOf('}') + 1;

        if (jsonStartIndex !== -1 && jsonEndIndex !== -1) {
          const jsonString = analysisResponse.substring(jsonStartIndex, jsonEndIndex);
          const parsedJson = JSON.parse(jsonString);

          // Đảm bảo cấu trúc knowledgeAnalysis.taskAnalyses là một mảng
          if (parsedJson.knowledgeAnalysis && !Array.isArray(parsedJson.knowledgeAnalysis.taskAnalyses)) {
            this.logger.warn('knowledgeAnalysis.taskAnalyses không phải là một mảng, sẽ đặt thành mảng rỗng.');
            parsedJson.knowledgeAnalysis.taskAnalyses = [];
          }
          // Điền các trường mặc định nếu thiếu cho mỗi taskAnalysis và chuyển đổi taskId
          if (parsedJson.knowledgeAnalysis && parsedJson.knowledgeAnalysis.taskAnalyses) {
            parsedJson.knowledgeAnalysis.taskAnalyses = parsedJson.knowledgeAnalysis.taskAnalyses.map(ta => {
              let finalTaskId = null;
              if (ta.taskId && typeof ta.taskId === 'string' && ta.taskId.startsWith('TASK_ID_')) {
                finalTaskId = ta.taskId.substring('TASK_ID_'.length);
                // Tùy chọn: Kiểm tra xem finalTaskId có phải là ObjectId hợp lệ không
                // if (!mongoose.Types.ObjectId.isValid(finalTaskId)) {
                //   this.logger.warn(`taskId trích xuất (${finalTaskId}) từ LLM không phải là ObjectId hợp lệ.`);
                //   finalTaskId = ta.taskId; // Giữ nguyên giá trị từ LLM nếu không hợp lệ
                // }
              } else {
                finalTaskId = ta.taskId || null; // Giữ nguyên nếu không đúng định dạng hoặc null
              }
              return {
                taskId: finalTaskId,
                taskName: ta.taskName || 'N/A',
                score: typeof ta.score === 'number' ? ta.score : 0,
                details: ta.details || 'Không có chi tiết.',
                makeOrBreak: Array.isArray(ta.makeOrBreak) ? ta.makeOrBreak : [],
                suggestions: Array.isArray(ta.suggestions) ? ta.suggestions : [],
              };
            });
          }

          return parsedJson;
        } else {
          // Nếu không tìm thấy định dạng JSON, trả về cấu trúc mặc định bao gồm các trường mới
          this.logger.warn('Không tìm thấy JSON hợp lệ trong phản hồi phân tích. Trả về cấu trúc mặc định.');
          return {
            summary: 'Không thể phân tích định dạng phản hồi',
            topInsights: ['Cần phân tích lại'],
            simulationScore: 0,
            knowledgeAnalysis: {
              proficiencyProcess: 'Không có thông tin',
              taskAnalyses: [], // Mảng rỗng theo cấu trúc mới
            },
            styleAnalysis: {
              score: 0,
              clarity: 'medium',
              pace: {wordsPerMinute: 0, evaluation: 'N/A'},
              fillerWords: {count: 0, evaluation: 'N/A'},
              sentenceLength: {average: 0, evaluation: 'N/A'},
              energy: 'medium',
            },
            trainerFeedback: {
              generalComments: 'Lỗi phân tích phản hồi',
              improvementSuggestions: [],
            },
            // Trường bổ sung
            emotionAnalysis: null,
            videoAnalysis: null,
            softSkillsAnalysis: null,
            managerFeedback: null,
          };
        }
      } catch (error) {
        this.logger.error('Lỗi khi phân tích phản hồi JSON:', error);
        // Trả về cấu trúc mặc định trong trường hợp lỗi nghiêm trọng
        return {
          summary: 'Không thể phân tích',
          topInsights: ['Cần phân tích lại'],
          simulationScore: 0,
          knowledgeAnalysis: {
            proficiencyProcess: 'Không thể phân tích',
            taskAnalyses: [],
          },
          styleAnalysis: {
            score: 0,
            clarity: 'medium',
            pace: {wordsPerMinute: 0, evaluation: 'N/A'},
            fillerWords: {count: 0, evaluation: 'N/A'},
            sentenceLength: {average: 0, evaluation: 'N/A'},
            energy: 'medium',
          },
          trainerFeedback: {
            generalComments: 'Không thể phân tích',
            improvementSuggestions: [],
          },
          emotionAnalysis: null,
          videoAnalysis: null,
          softSkillsAnalysis: null,
          managerFeedback: null,
        };
      }
    },

    /**
     * Đánh giá tốc độ nói
     */
    evaluatePace(wordsPerMinute) {
      if (wordsPerMinute < 120) return 'quá chậm';
      if (wordsPerMinute > 180) return 'quá nhanh';
      return 'tốt';
    },

    /**
     * Đánh giá từ đệm
     */
    evaluateFillerWords(percentage) {
      if (percentage < 3) return 'rất tốt';
      if (percentage < 7) return 'tốt';
      if (percentage < 15) return 'cần cải thiện';
      return 'quá nhiều từ đệm';
    },

    /**
     * Đánh giá độ dài câu
     */
    evaluateSentenceLength(averageLength) {
      if (averageLength < 5) return 'câu quá ngắn';
      if (averageLength > 30) return 'câu quá dài';
      return 'độ dài câu phù hợp';
    },

    /**
     * Định dạng thời gian (từ startTime đến endTime)
     */
    formatDuration(startTime, endTime) {
      if (!startTime) return 'Không có thông tin';

      const start = new Date(startTime);
      let end = endTime ? new Date(endTime) : new Date();

      // Tính thời lượng (ms)
      const duration = end - start;
      const minutes = Math.floor(duration / 60000);
      const seconds = Math.floor((duration % 60000) / 1000);

      return `${minutes} phút ${seconds} giây`;
    },
  },

  created() {
    // Khởi tạo service
  },

  async started() {
    // Khởi tạo khi service được khởi động
  },

  async stopped() {
    // Dọn dẹp khi service dừng
  },
};
