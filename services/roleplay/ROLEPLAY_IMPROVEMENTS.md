# Cải Tiến Hệ Thống Roleplay - Transcript và Turn Management

## Tổng Quan

Tài liệu này mô tả các cải tiến đã được thực hiện để giải quyết hai vấn đề chính trong hệ thống roleplay:

1. **Transcript âm thanh không đầy đủ** khi người dùng nói trong thời gian dài (đến 1 phút)
2. **AI chen ngang trong turn của người dùng** trước khi người dùng hoàn thành phát biểu

## Phân Tích Vấn Đề

### Vấn Đề 1: Transcript Không Đầy Đủ

**Nguyên nhân gốc rễ:**
- Microsoft Speech SDK có timeout mặc định quá ngắn
- Buffer size không đủ cho audio dài
- Thiếu cơ chế xử lý audio chunks lớn
- Không có thống kê để theo dõi performance

**Triệu chứng:**
- Transcript bị cắt ngắn với audio > 30 giây
- M<PERSON>t dữ liệu khi người dùng nói liên tục
- Không có thông báo lỗi rõ ràng

### Vấn Đề 2: AI Chen Ngang

**Nguyên nhân gốc rễ:**
- Silence threshold quá ngắn (2 giây)
- Không có cơ chế double-check
- VAD có false positive với silence ngắn
- AI được trigger ngay khi phát hiện silence

**Triệu chứng:**
- AI bắt đầu phản hồi khi người dùng chỉ tạm dừng
- Trải nghiệm người dùng bị gián đoạn
- Cuộc hội thoại không tự nhiên

## Giải Pháp Đã Thực Hiện

### 1. Cải Tiến Microsoft Speech SDK

**File:** `services/roleplay/speechprocessing.service.js`

```javascript
// Tăng timeout cho session dài
speechConfig.setProperty(sdk.PropertyId.SpeechServiceConnection_InitialSilenceTimeoutMs, "300000"); // 5 phút
speechConfig.setProperty(sdk.PropertyId.SpeechServiceConnection_EndSilenceTimeoutMs, "300000"); // 5 phút

// Cải thiện độ chính xác
speechConfig.setProperty(sdk.PropertyId.Speech_SegmentationSilenceTimeoutMs, "2000");
speechConfig.setProperty(sdk.PropertyId.SpeechServiceConnection_RecoMode, "CONVERSATION");
speechConfig.outputFormat = sdk.OutputFormat.Detailed;
```

**Cải tiến pushAudioToStream:**
- Thêm kiểm tra trạng thái stream
- Thống kê bytes và chunks
- Error handling tốt hơn

**Cải tiến closeSpeechStream:**
- Delay 500ms để xử lý audio còn lại
- Timeout protection (5 giây)
- Logging thống kê chi tiết

### 2. Cải Tiến VAD Configuration

**File:** `services/roleplay/roleplaysessions/audioUtils.js`

```javascript
const VAD_DEFAULTS = {
  threshold: 0.6, // Tăng từ 0.5 để giảm false positive
  minSpeechDuration: 0.5, // Tăng từ 0.3 giây
  minSilenceDuration: 1.0, // Tăng từ 0.5 giây
  bufferSizeInSecondsForVad: 120, // Tăng từ 60 giây
};
```

### 3. Cải Tiến Turn Management

**File:** `constants/constant.js`

```javascript
exports.AUDIO_PROCESSING_DEFAULTS = {
  silenceThreshold: 3000, // Tăng từ 2000ms
  confirmationSilenceThreshold: 1500, // Mới thêm
  maxTurnDuration: 120000, // Mới thêm - 2 phút
};
```

**File:** `mixins/roleplaySTT.mixin.js`

**Cơ chế Double-Check:**
1. Phát hiện silence > 3 giây
2. Bắt đầu quá trình xác nhận (1 giây)
3. Kiểm tra lại voice activity
4. Chỉ kết thúc turn nếu thực sự im lặng

**Bảo vệ chống turn quá dài:**
- Tự động kết thúc turn sau 2 phút
- Logging cảnh báo

### 4. Cải Tiến AI Response Prevention

**File:** `mixins/roleplayLLM.mixin.js`

**Multiple Checkpoints:**
1. Kiểm tra trước khi bắt đầu LLM
2. Delay 300ms để double-check
3. Kiểm tra trong quá trình streaming
4. Kiểm tra trước TTS
5. Kiểm tra trong quá trình audio streaming

**Enhanced Interruption Detection:**
```javascript
// Kiểm tra cả hai điều kiện
if (state.isAiInterruptedByStudent || state.isStudentSpeaking) {
  // Dừng ngay lập tức
}
```

### 5. Cải Tiến Connection State

**File:** `services/roleplay/roleplaysessions/roleplaysessions.service.js`

Thêm các trường mới để hỗ trợ turn management tốt hơn:
- `turnEndingConfirmation`: Quản lý xác nhận kết thúc turn
- `startTurnTime`: Theo dõi thời gian turn

*Lưu ý: Các trường `isAiResponding` và `isAiInterruptedByStudent` đã có sẵn trong hệ thống.*

## Kết Quả Đạt Được

### ✅ Vấn Đề 1: Transcript Đầy Đủ
- Hỗ trợ audio dài đến 5 phút (tăng từ ~30 giây)
- Cải thiện độ chính xác transcript
- Thống kê chi tiết để debugging
- Error handling tốt hơn

### ✅ Vấn Đề 2: Ngăn Chặn AI Chen Ngang
- Tăng silence threshold lên 3 giây
- Cơ chế double-check với 1 giây xác nhận
- Multiple layers protection
- Trải nghiệm người dùng tự nhiên hơn

## Testing và Validation

**Test Script:** `services/roleplay/test-improvements.js`

Tất cả test cases đã pass:
- ✅ Configuration updates
- ✅ Turn management logic
- ✅ Speech SDK improvements
- ✅ Audio streaming enhancements
- ✅ Transcript completeness

## Hướng Dẫn Sử Dụng

### 1. Chạy Test
```bash
node services/roleplay/test-improvements.js
```

### 2. Monitoring
Các log mới được thêm để theo dõi:
- Stream statistics (bytes, chunks, duration)
- Turn management events
- AI response prevention
- Interruption detection

### 3. Configuration
Có thể điều chỉnh các tham số trong:
- `constants/constant.js` - Audio processing defaults
- `audioUtils.js` - VAD configuration
- `speechprocessing.service.js` - Speech SDK settings

## Tác Động và Lưu Ý

### Tích Cực
- Transcript chính xác hơn cho audio dài
- Trải nghiệm người dùng tự nhiên hơn
- Ít bị gián đoạn trong cuộc hội thoại
- Debugging tốt hơn với logging chi tiết

### Cần Lưu Ý
- Thời gian phản hồi AI tăng nhẹ (3 giây thay vì 2 giây)
- Sử dụng nhiều memory hơn cho buffer lớn
- Cần monitor performance trong production

### Backward Compatibility
- Tất cả thay đổi đều backward compatible
- Không ảnh hưởng đến API hiện tại
- Có thể rollback dễ dàng nếu cần

## Kết Luận

Các cải tiến đã giải quyết thành công cả hai vấn đề chính:
1. **Transcript đầy đủ** cho audio dài đến 5 phút
2. **Ngăn chặn AI chen ngang** với cơ chế double-check

Hệ thống roleplay giờ đây hoạt động ổn định và mang lại trải nghiệm người dùng tốt hơn đáng kể.
