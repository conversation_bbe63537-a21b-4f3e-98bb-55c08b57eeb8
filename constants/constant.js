exports.USER_CODES = {
  SYSTEM_ADMIN: 'SYSTEM_ADMIN',
  ORG_ADMIN: 'admin',
  CONTRIBUTOR: 'contributor',
  NORMAL: 'normal',
  TEACHER: 'teacher',
  STUDENT: 'student',
};

exports.PERMISSION_ACCESS = {
  NO_PERMISSION: 'NO_PERMISSION',
  OWNER: 'OWNER',
  EDITOR: 'EDITOR',
  VIEWER: 'VIEWER',
};

exports.INPUT_TYPE = {
  TEXT: "text",
  AUDIO: "audio",
  AUDIO_STREAM: "audio_stream",
  VIDEO: "video",
  IMAGE: "image",
  FILE: "file",
  TTS: "text_to_speech",
}

exports.OUTPUT_TYPE = {
  TEXT: "text",
  AUDIO: "audio",
  HTML: "html",
}

exports.USER_STATE = {
  ACTIVE: "active",
  WAITLIST: "waitlist",
}

exports.MEDIA_INPUT_TYPE = [
  "video",
  "audio",
  "image",
  "text_to_speech",
]

// Cấu hình cho audio processing
exports.AUDIO_PROCESSING_DEFAULTS = {
  silenceThreshold: 2000,
  confirmationSilenceThreshold: 1000, // Thời gian silence ngắn hơn để bắt đầu xác nhận kết thúc turn
  maxTurnDuration: 120000, // Thời gian tối đa cho một turn (2 phút)
};

exports.PERSONA = {
  TEACHER_FOR_GRADES_1_2_3: 'TEACHER_FOR_GRADES_1_2_3',
  LECTURER_AT_UNIVERSITY: 'LECTURER_AT_UNIVERSITY',
  LANGUAGE_CENTER: 'LANGUAGE_CENTER',
  IELTS_CENTER: 'IELTS_CENTER',
  INDEPENDENT_TEACHER: 'INDEPENDENT_TEACHER',
  ENGLISH_CENTER_MANAGER: 'ENGLISH_CENTER_MANAGER',
  OTHER: 'OTHER',
};
